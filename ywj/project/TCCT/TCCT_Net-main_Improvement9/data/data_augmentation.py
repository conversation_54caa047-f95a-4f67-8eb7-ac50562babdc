import torch  # 导入 PyTorch 库，用于张量操作
import numpy as np  # 导入 numpy 库，用于数值计算


def interaug(data, label, batch_size, signal_length, device, num_behavioral_features):
    """
    通过分段和重组 (Segmentation and Recombination, S&R) 技术进行数据增强。

    参数:
        data (np.array): 需要增强的数据。
        label (np.array): 输入数据对应的标签。
        batch_size (int): 批次大小。
        signal_length (int): 信号的长度。
        device (torch.device): 用于存储张量的设备（CPU/GPU）。
        num_behavioral_features (int): 数据中行为特征的数量。

    返回:
        torch.Tensor: 增强后的数据张量。
        torch.Tensor: 增强后的标签张量。
    """

    aug_data, aug_label = [], []  # 初始化存储增强数据和标签的列表
    
    # 定义分段数量、每个分段的长度以及每个类别的增强样本数量
    n_segments = 30  # 将信号分为 30 段
    segment_length = signal_length // n_segments  # 每段的长度
    total_samples_per_class = batch_size // len(np.unique(label))  # 每个类别的增强样本数量

    # 遍历每个唯一的类别
    for cls4aug in np.unique(label):
        # 找到当前类别的索引，并提取对应的数据
        cls_idx = np.where(label == cls4aug)
        tmp_data = data[cls_idx]

        # 确定需要增强的样本数量以匹配 total_samples_per_class
        n_samples_needed = total_samples_per_class
        # 初始化一个临时数组用于存储当前类别的增强数据
        tmp_aug_data = np.zeros((n_samples_needed, 1, num_behavioral_features, signal_length))

        # 执行数据增强
        for ri in range(n_samples_needed):
            for rj in range(n_segments):
                # 为每个分段随机选择当前类别数据的索引
                rand_idx = np.random.randint(0, len(tmp_data), n_segments)
                start = rj * segment_length  # 分段的起始位置
                end = (rj + 1) * segment_length  # 分段的结束位置
                
                # 将增强数据中的分段替换为随机选择的分段
                tmp_aug_data[ri, :, :, start:end] = tmp_data[rand_idx[rj], :, :, start:end]

        aug_data.append(tmp_aug_data)  # 将增强数据添加到列表中
        aug_label.append(np.full(n_samples_needed, cls4aug))  # 将增强标签添加到列表中

    # 将所有增强数据和标签拼接在一起，然后打乱顺序
    aug_data = np.concatenate(aug_data)
    aug_label = np.concatenate(aug_label)
    aug_shuffle = np.random.permutation(len(aug_data))  # 生成随机排列索引
    aug_data = aug_data[aug_shuffle, :, :]  # 打乱增强数据
    aug_label = aug_label[aug_shuffle]  # 打乱增强标签

    # 将 numpy 数组转换为 PyTorch 张量，并转移到指定设备
    aug_data = torch.from_numpy(aug_data).to(device).float()
    aug_label = torch.from_numpy(aug_label).to(device).long()

    return aug_data, aug_label  # 返回增强后的数据和标签