import math  # 导入 math 模块，用于数学运算
import torch.nn.functional as F  # 导入 PyTorch 的函数模块，包含激活函数等

import torch  # 导入 PyTorch 库
from torch import nn  # 导入 PyTorch 的神经网络模块
from einops import rearrange  # 导入 einops 的 rearrange 函数，用于张量重排
from einops.layers.torch import Rearrange  # 导入 einops 的 Rearrange 层，用于定义模型中的重排操作

from torch.backends import cudnn  # 导入 CUDA 后端模块
cudnn.benchmark = False  # 禁用 CUDA 的自动优化，以确保结果可复现
cudnn.deterministic = True  # 设置 CUDA 操作是确定性的

# ✅ CBAM 模块（通道 + 空间注意力）
class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=8):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.shared_mlp = nn.Sequential(
            nn.Linear(in_planes, in_planes // ratio),
            nn.ReLU(),
            nn.Linear(in_planes // ratio, in_planes)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        b, c, _, _ = x.size()
        avg_out = self.shared_mlp(self.avg_pool(x).view(b, c))
        max_out = self.shared_mlp(self.max_pool(x).view(b, c))
        out = self.sigmoid(avg_out + max_out).view(b, c, 1, 1)
        return x * out.expand_as(x)

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x_cat = torch.cat([avg_out, max_out], dim=1)
        attn = self.sigmoid(self.conv(x_cat))
        return x * attn

class CBAM(nn.Module):
    def __init__(self, channels, ratio=8, kernel_size=7):
        super().__init__()
        self.ca = ChannelAttention(channels, ratio)
        self.sa = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.ca(x)
        x = self.sa(x)
        return x


class TS_ConvModule(nn.Module):
    """
    用于处理时间-空间数据并准备进行 Transformer 处理的卷积模块。

    属性:
        shallownet (nn.Sequential): 由卷积、批量归一化、激活函数、池化和 Dropout 层组成的序列容器。
        projection (nn.Sequential): 卷积层和重排层，用于将输出形状调整为适合 Transformer 处理的格式。
    """

    def __init__(self):
        super().__init__()
        # 类似 shallownet 的卷积网络，用于提取初始特征
        self.shallownet = nn.Sequential(
            nn.Conv2d(1, 40, (1, 25), (1, 1)),  # 2D 卷积层，输入通道为 1，输出通道为 40，卷积核大小为 (1, 25)
            nn.Conv2d(40, 40, (2, 1), (1, 1)),  # 2D 卷积层，输入通道为 40，输出通道为 40，卷积核大小为 (2, 1) 
            nn.BatchNorm2d(40),  # 批量归一化层，对 40 个通道进行归一化
            nn.ELU(),  # ELU 激活函数
            nn.AvgPool2d((1, 75), (1, 15)),  # 平均池化层，用于时间切片
            nn.Dropout(0.5),  # Dropout 层，丢弃率为 50%
        )

        self.cbam = CBAM(40)  # 👈 添加 CBAM，通道数是卷积后的 40

        # 投影层，用于将输出形状调整为适合 Transformer 处理的格式
        self.projection = nn.Sequential(
            nn.Conv2d(40, 40, (1, 1), stride=(1, 1)),  # 2D 卷积层，输入通道为 40，输出通道为 40，卷积核大小为 (1, 1)
            Rearrange('b e (h) (w) -> b (h w) e'),  # 重排层，将张量形状从 [batch_size, channels, height, width] 转换为 [batch_size, sequence_length, embedding_size]
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        TS_ConvModule 的前向传播函数。

        参数:
            x (torch.Tensor): 输入张量，形状为 (batch_size, channels, height, width)。

        返回:
            torch.Tensor: 经过重排后的输出张量，形状为 [batch_size, sequence_length, embedding_size]。
        """

        x = self.shallownet[:3](x)  # 卷积 + BN
        x = self.cbam(x)            # ✅ 加入 CBAM 注意力
        x = self.shallownet[3:](x)  # 激活 + 池化 + dropout
        x = self.projection(x)  # 通过投影层调整形状
        return x


class TS_AttentionModule(nn.Module):
    """
    用于 Transformer 的 TS_AttentionModule。

    参数:
        emb_size (int): 每个嵌入向量的大小。
        num_heads (int): 注意力头的数量。
        dropout (float): 注意力权重的 Dropout 率。

    属性:
        keys (nn.Linear): 用于生成键向量的线性层。
        queries (nn.Linear): 用于生成查询向量的线性层。
        values (nn.Linear): 用于生成值向量的线性层。
        att_drop (nn.Dropout): 注意力权重的 Dropout 层。
        projection (nn.Linear): 最终的投影层。
    """

    def __init__(self, emb_size, num_heads, dropout):
        """
        初始化 TS_AttentionModule。

        参数:
            emb_size (int): 每个嵌入向量的大小。
            num_heads (int): 注意力头的数量。
            dropout (float): 注意力权重的 Dropout 率。
        """

        super().__init__()
        self.emb_size = emb_size
        self.num_heads = num_heads
        self.keys = nn.Linear(emb_size, emb_size)  # 线性层，用于生成键向量
        self.queries = nn.Linear(emb_size, emb_size)  # 线性层，用于生成查询向量
        self.values = nn.Linear(emb_size, emb_size)  # 线性层，用于生成值向量
        self.att_drop = nn.Dropout(dropout)  # Dropout 层，用于注意力权重
        self.projection = nn.Linear(emb_size, emb_size)  # 线性层，用于最终投影

    def forward(self, x: torch.Tensor, mask: torch.Tensor = None) -> torch.Tensor:
        """
        TS_AttentionModule 的前向传播函数。

        参数:
            x (torch.Tensor): 输入张量。
            mask (torch.Tensor, optional): 应用于注意力权重的掩码。

        返回:
            torch.Tensor: 经过注意力和线性变换后的输出张量。
        """
        queries = rearrange(self.queries(x), "b n (h d) -> b h n d", h=self.num_heads)  # 重排查询向量
        keys = rearrange(self.keys(x), "b n (h d) -> b h n d", h=self.num_heads)  # 重排键向量
        values = rearrange(self.values(x), "b n (h d) -> b h n d", h=self.num_heads)  # 重排值向量
        energy = torch.einsum('bhqd, bhkd -> bhqk', queries, keys)  # 计算注意力能量
        if mask is not None:
            fill_value = torch.finfo(torch.float32).min
            energy.mask_fill(~mask, fill_value)  # 应用掩码

        scaling = self.emb_size ** 0.5  # 缩放因子
        att = F.softmax(energy / scaling, dim=-1)  # 计算注意力权重
        att = self.att_drop(att)  # 应用 Dropout
        out = torch.einsum('bhal, bhlv -> bhav ', att, values)  # 计算加权值
        out = rearrange(out, "b h n d -> b n (h d)")  # 重排输出
        out = self.projection(out)  # 最终投影
        return out  # 返回输出张量


class TS_ResidualAdd(nn.Module):
    """
    残差连接后接层归一化。
    包装一个函数模块，将模块的输出添加到输入中，然后进行归一化。

    属性:
        fn (nn.Module): 应用残差连接的模块。
    """
    def __init__(self, fn):
        """
        初始化 TS_ResidualAdd 模块。

        参数:
            fn (nn.Module): 应用残差连接的模块。
        """

        super().__init__()
        self.fn = fn

    def forward(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        残差连接的前向传播函数。
        
        参数:
            x (torch.Tensor): 输入张量，残差将添加到该张量。

        返回:
            torch.Tensor: 添加残差并应用模块后的输出张量。
        """
        res = x
        x = self.fn(x, **kwargs)  # 应用模块
        x += res  # 添加残差
        return x  # 返回输出张量


class TS_FeedForwardBlock(nn.Sequential):
    """
    前馈神经网络块。

    参数:
        emb_size (int): 输入和输出张量的维度。
        expansion (int): 中间层的扩展因子。
        drop_p (float): Dropout 概率。
    """
    def __init__(self, emb_size, expansion, drop_p):
        """
        初始化 TS_FeedForwardBlock。

        参数:
            emb_size (int): 输入和输出张量的维度。
            expansion (int): 中间层的扩展因子。
            drop_p (float): Dropout 概率。
        """        
        super().__init__(
            nn.Linear(emb_size, expansion * emb_size),  # 线性层，扩展维度
            nn.GELU(),  # GELU 激活函数
            nn.Dropout(drop_p),  # Dropout 层
            nn.Linear(expansion * emb_size, emb_size),  # 线性层，恢复维度
        )
        
        
class GELU(nn.Module):
    """
    高斯误差线性单元 (GELU) 激活函数。
    """
    def forward(self, input: torch.Tensor) -> torch.Tensor:
        """
        对输入张量应用 GELU 激活函数。

        参数:
            input (torch.Tensor): 输入张量。

        返回:
            torch.Tensor: 应用 GELU 后的输出张量。
        """
        return input * 0.5 * (1.0 + torch.erf(input / math.sqrt(2.0)))  # GELU 公式
    
    
class TS_TransformerEncoderBlock(nn.Sequential):
    """
    结合 TS_AttentionModule 和 TS_FeedForwardBlock 的 Transformer 编码器块，带有残差连接。

    参数:
        emb_size (int): 嵌入大小。
        num_heads (int): 注意力头的数量。
        drop_p (float): 注意力和前馈层中的 Dropout 率。
        forward_expansion (int): 前馈块的扩展因子。
        forward_drop_p (float): 前馈块中的 Dropout 率。
    """
    def __init__(self, emb_size, num_heads=10, drop_p=0.5, forward_expansion=4, forward_drop_p=0.5):
        """
        初始化 TS_TransformerEncoderBlock。

        参数:
            emb_size (int): 嵌入大小。
            num_heads (int): 注意力头的数量。
            drop_p (float): 注意力和前馈层中的 Dropout 率。
            forward_expansion (int): 前馈块的扩展因子。
            forward_drop_p (float): 前馈块中的 Dropout 率。
        """
        super().__init__(
            TS_ResidualAdd(nn.Sequential(
                nn.LayerNorm(emb_size),  # 层归一化
                TS_AttentionModule(emb_size, num_heads, drop_p),  # 注意力模块
                nn.Dropout(drop_p)  # Dropout 层
            )),
            TS_ResidualAdd(nn.Sequential(
                nn.LayerNorm(emb_size),  # 层归一化
                TS_FeedForwardBlock(emb_size, forward_expansion, forward_drop_p),  # 前馈块
                nn.Dropout(drop_p)  # Dropout 层
            ))
        )


class TS_TransformerEncoder(nn.Sequential):
    """
    TS_TransformerEncoderBlock 的序列容器。

    参数:
        depth (int): Transformer 编码器块的层数。
        emb_size (int): Transformer 中使用的嵌入大小。
    """
    def __init__(self, depth, emb_size):
        """
        初始化 TS_TransformerEncoder。

        参数:
            depth (int): Transformer 编码器块的层数。
            emb_size (int): Transformer 中使用的嵌入大小。
        """        
        super().__init__(*[TS_TransformerEncoderBlock(emb_size) for _ in range(depth)])  # 创建多个编码器块


class TS_Stream(nn.Module):
    """
    使用卷积和 Transformer 编码器块处理时间-空间数据的模块。

    参数:
        depth (int): TS_TransformerEncoder 中的层数。
        emb_size (int): 嵌入空间的维度。

    属性:
        TS_ConvModule (TS_ConvModule): 用于初始数据处理的 TS_ConvModule 模块。
        TS_TransformerEncoder (TS_TransformerEncoder): 由多个层组成的 Transformer 编码器。
    """
    def __init__(self, depth=5, emb_size=40):
        """
        初始化 TS_Stream。

        参数:
            depth (int): TS_TransformerEncoder 中的层数。
            emb_size (int): 嵌入空间的维度。
        """
        super().__init__()
        self.TS_ConvModule = TS_ConvModule()  # 初始化 TS_ConvModule
        self.TS_TransformerEncoder = TS_TransformerEncoder(depth, emb_size)  # 初始化 TS_TransformerEncoder

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        TS_Stream 的前向传播函数。

        参数:
            x (torch.Tensor): 输入的待处理张量。

        返回:
            torch.Tensor: 经过 TS_ConvModule 和 TS_TransformerEncoder 处理后的输出张量。
        """
        x = self.TS_ConvModule(x)  # 通过 TS_ConvModule 处理数据
        x = self.TS_TransformerEncoder(x)  # 通过 TS_TransformerEncoder 处理数据

        return x  # 返回输出张量
