import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import confusion_matrix

def plot_confusion_matrix(y_true, y_pred, class_names=None, title="Confusion Matrix", save_path="confusion_matrix.png"):
    """
    绘制混淆矩阵图（移除seaborn依赖）
    """
    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    # 设置默认类别名称
    if class_names is None:
        class_names = [f'Class {i}' for i in range(len(cm))]
    
    # 创建图形
    plt.figure(figsize=(8, 6), dpi=300)
    
    # 绘制热力图（使用matplotlib原生功能）
    im = plt.imshow(cm, interpolation='nearest', cmap='Blues')
    plt.colorbar(im, label='Count')
    
    # 设置标签
    tick_marks = np.arange(len(class_names))
    plt.xticks(tick_marks, class_names, rotation=45)
    plt.yticks(tick_marks, class_names)
    
    # 添加数值标注
    thresh = cm.max() / 2.
    for i, j in np.ndindex(cm.shape):
        plt.text(j, i, format(cm[i, j], 'd'),
                horizontalalignment="center",
                color="white" if cm[i, j] > thresh else "black")
    
    plt.title(title, fontsize=14, fontweight='bold')
    plt.xlabel('Predicted Label', fontsize=12)
    plt.ylabel('True Label', fontsize=12)
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    print(f'Confusion matrix saved to {save_path}')

def plot_all_confusion_matrices(y_true, y_pred, class_names=None):
    """
    绘制所有参与度级别的混淆矩阵
    
    参数:
        y_true (tensor): 真实标签
        y_pred (tensor): 预测标签
        class_names (list): 类别名称列表
    """
    # 转换为numpy数组
    if hasattr(y_true, 'cpu'):
        y_true = y_true.cpu().numpy()
    if hasattr(y_pred, 'cpu'):
        y_pred = y_pred.cpu().numpy()
    
    # 设置参与度类别名称 - 使用正确的标签映射
    if class_names is None:
        class_names = ['Not-Engaged', 'Barely-engaged', 'Engaged', 'Highly-Engaged']
    
    # 绘制总体混淆矩阵
    plot_confusion_matrix(y_true, y_pred, class_names, 
                         "Overall Student Engagement Confusion Matrix", 
                         "overall_confusion_matrix.png")
    
    # 为每个类别绘制二分类混淆矩阵
    for i, class_name in enumerate(class_names):
        # 创建二分类标签 (当前类别 vs 其他)
        y_true_binary = (y_true == i).astype(int)
        y_pred_binary = (y_pred == i).astype(int)
        
        binary_class_names = [f'Not {class_name}', class_name]
        plot_confusion_matrix(y_true_binary, y_pred_binary, binary_class_names,
                             f"{class_name} vs Others Confusion Matrix",
                             f"{class_name.lower().replace('-', '_')}_confusion_matrix.png")

def plot_metrics(train_losses, train_accuracies, test_accuracies):
    """
    绘制训练损失、训练准确率和测试准确率随 epoch 的变化图。

    参数:
        train_losses (list of float): 每个 epoch 的训练损失列表。
        train_accuracies (list of float): 每个 epoch 的训练准确率列表。
        test_accuracies (list of float): 每个 epoch 的测试准确率列表。
    """
    
    # 确定 epoch 的数量
    epochs = range(1, len(train_losses) + 1)

    # 创建训练损失的绘图
    fig, ax1 = plt.subplots(dpi=300)  # 创建一个图形和子图，设置分辨率为 300
    color = 'tab:red'  # 设置训练损失曲线的颜色为红色
    ax1.set_xlabel('Epoch')  # 设置 x 轴标签为 "Epoch"
    ax1.set_ylabel('Train Loss', color=color)  # 设置 y 轴标签为 "Train Loss"，并指定颜色
    ax1.plot(epochs, train_losses, color=color)  # 绘制训练损失曲线
    ax1.tick_params(axis='y', labelcolor=color)  # 设置 y 轴刻度的颜色

    # 创建第二个 y 轴以绘制训练准确率
    ax2 = ax1.twinx()  # 创建共享 x 轴的第二个 y 轴
    color = 'tab:blue'  # 设置训练准确率曲线的颜色为蓝色
    ax2.set_ylabel('Train Accuracy', color=color)  # 设置第二个 y 轴标签为 "Train Accuracy"，并指定颜色
    ax2.plot(epochs, train_accuracies, color=color)  # 绘制训练准确率曲线
    ax2.tick_params(axis='y', labelcolor=color)  # 设置第二个 y 轴刻度的颜色

    fig.tight_layout()  # 调整子图布局以避免重叠
    fig.savefig('train_metrics.png', bbox_inches='tight')  # 将图形保存为 train_metrics.png
    print('\nPlot of training loss and training accuracy saved to train_metrics.png')  # 打印保存成功的提示信息

    # 绘制测试准确率
    fig, ax = plt.subplots(dpi=300)  # 创建一个新的图形和子图，设置分辨率为 300
    ax.set_xlabel('Epoch')  # 设置 x 轴标签为 "Epoch"
    ax.set_ylabel('Test Accuracy')  # 设置 y 轴标签为 "Test Accuracy"
    ax.plot(epochs, test_accuracies, label='Test Accuracy')  # 绘制测试准确率曲线
    ax.legend()  # 添加图例

    fig.tight_layout()  # 调整子图布局以避免重叠
    fig.savefig('test_accuracy.png', bbox_inches='tight')  # 将图形保存为 test_accuracy.png
    print('Plot of test accuracy saved to test_accuracy.png')  # 打印保存成功的提示信息


def log_metrics(epoch, train_loss, test_loss, train_acc, test_acc, best_acc, duration, learning_rate):
    """
    将训练和测试的指标记录到控制台。

    参数:
        epoch (int): 当前的 epoch 编号。
        train_loss (float): 当前 epoch 的训练损失。
        test_loss (float): 当前 epoch 的测试损失。
        train_acc (float): 当前 epoch 的训练准确率。
        test_acc (float): 当前 epoch 的测试准确率。
        best_acc (float): 当前观察到的最佳测试准确率。
        duration (float): 当前 epoch 的持续时间（秒）。
        learning_rate (float): 当前的学习率。
    """
    print(f'Epoch: {epoch + 1}',  # 打印当前 epoch 编号
          f'Train Loss: {train_loss:.4f}',  # 打印训练损失，保留 4 位小数
          f'Test Loss: {test_loss:.4f}',  # 打印测试损失，保留 4 位小数
          f'Train Acc: {train_acc:.4f}',  # 打印训练准确率，保留 4 位小数
          f'Test Acc: {test_acc:.4f}',  # 打印测试准确率，保留 4 位小数
          f'Best Acc: {best_acc:.4f}',  # 打印最佳测试准确率，保留 4 位小数
          f'Time: {duration:.2f}s',  # 打印 epoch 的持续时间，保留 2 位小数
          f'LR: {learning_rate:.6f}',  # 打印当前学习率，保留 6 位小数
          sep='  |  ')  # 设置分隔符为 "  |  "
