import numpy as np  # 导入 numpy 库，用于数值计算
import torch  # 导入 PyTorch 库，用于张量操作和模型评估
from data.data_processing import batch_cwt  # 从 data_processing 模块中导入 batch_cwt 函数，用于计算连续小波变换 (CWT)


def batch_stft(batch_signal, n_fft=64, hop_length=32, win_length=64):
    """
    对一批输入信号执行短时傅里叶变换 (STFT)。

    参数:
        batch_signal (torch.Tensor): 输入信号，形状为 (B, C, H, W)，本任务中 (B, 1, n_features, time)
        n_fft (int): FFT 的窗口大小。
        hop_length (int): 每次滑动的步长。
        win_length (int): 窗口长度。

    返回:
        torch.Tensor: STFT 特征，形状为 (B, C, freq_bins, time_bins)
    """
    B, C, H, W = batch_signal.shape
    batch_stft_result = []

    for b in range(B):
        channels_stft = []
        for c in range(H):  # 遍历每个特征通道
            signal = batch_signal[b, 0, c, :]  # shape: (W,)
            stft_result = torch.stft(signal, n_fft=n_fft, hop_length=hop_length,
                                     win_length=win_length, return_complex=True)
            magnitude = torch.abs(stft_result)  # 转换为幅度谱
            channels_stft.append(magnitude.unsqueeze(0))  # shape: (1, freq, time)
        stft_stack = torch.stack(channels_stft, dim=0)  # shape: (H, 1, freq, time)
        batch_stft_result.append(stft_stack)

    result = torch.stack(batch_stft_result, dim=0)  # shape: (B, H, 1, freq, time)
    result = result.permute(0, 2, 1, 3, 4).squeeze(1)  # shape: (B, H, freq, time)
    return result  # shape: (B, H, freq, time)


def evaluate(model, test_data, test_label, criterion_cls, freq_min, freq_max, tensor_height, sampling_frequency):
    """
    在测试数据集上评估模型。
    
    参数:
        model (nn.Module): 神经网络模型。
        test_data (torch.Tensor): 测试数据集的特征。
        test_label (torch.Tensor): 测试数据集的标签。
        criterion_cls (nn.Module): 用于评估的损失函数。
        freq_min (float): CWT 的最小频率。
        freq_max (float): CWT 的最大频率。
        tensor_height (int): CWT 的离散频率数量。
        sampling_frequency (int): 信号的采样频率。

    返回:
        float: 测试准确率。
        float: 测试损失。
        torch.Tensor: 预测的标签。
    """
    
    model.eval()  # 将模型设置为评估模式
    
    # 生成 CWT 的频率范围
    frequencies = np.linspace(freq_min, freq_max, tensor_height)
    # print("/////////////////////////////////////")
    # print("test_data shape:", test_data.shape)
    # print("test_labels shape:", test_label.shape)
    # 计算测试数据的 CWT 表示
    cwt_representations_test = batch_cwt(test_data, frequencies, sampling_frequency=sampling_frequency)
    
    with torch.no_grad():  # 禁用梯度计算
        # 通过模型进行预测
        Cls = model(test_data, cwt_representations_test)
        # 计算测试损失
        loss_test = criterion_cls(Cls, test_label)
        
        # 计算预测标签和准确率
        y_pred = torch.max(Cls, 1)[1]  # 获取预测标签
        acc = float((y_pred == test_label).cpu().numpy().astype(int).sum()) / float(test_label.size(0))  # 计算准确率
    
    return acc, loss_test.item(), y_pred  # 返回准确率、测试损失和预测标签